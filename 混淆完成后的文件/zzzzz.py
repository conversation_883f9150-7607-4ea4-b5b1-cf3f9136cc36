import argparse
import re

def extract_original_names(file_path, name_types=None):
    """
    从指定文件中提取原始名称。
    
    Args:
        file_path (str): 文件路径
        name_types (list): 要提取的名称类型列表，如 ['属性名', '方法名', '代理名']
                          如果为 None，则提取所有支持的类型
    
    Returns:
        dict: 按类型分组的名称集合
    """
    # 支持的名称类型
    supported_types = ['属性名', '方法名', '代理名', '枚举名', '常量名', 'Block名', '文件名', '局部变量名']
    
    if name_types is None:
        name_types = supported_types
    
    # 验证输入的名称类型
    invalid_types = [t for t in name_types if t not in supported_types]
    if invalid_types:
        print(f"警告：不支持的名称类型: {invalid_types}")
        name_types = [t for t in name_types if t in supported_types]
    
    if not name_types:
        print("错误：没有有效的名称类型")
        return {}
    
    results = {name_type: set() for name_type in name_types}
    
    # 为每种类型创建正则表达式
    patterns = {}
    for name_type in name_types:
        patterns[name_type] = re.compile(rf'原{name_type}:(.*?)(?=:|被修改为)')
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                for name_type, pattern in patterns.items():
                    match = pattern.search(line)
                    if match:
                        name = match.group(1).strip()
                        if name:
                            results[name_type].add(name)
    except Exception as e:
        print(f"无法读取文件 {file_path}: {e}")
    
    return results

def compare_names(file1, file2, output_file, name_types=None):
    """
    比较两个文件中的名称，找出新增、删除和共同的名称，并将结果写入输出文件。
    
    Args:
        file1 (str): 第一个文件路径
        file2 (str): 第二个文件路径
        output_file (str): 输出文件路径
        name_types (list): 要比较的名称类型列表
    """
    names1 = extract_original_names(file1, name_types)
    names2 = extract_original_names(file2, name_types)
    
    # 获取所有出现的名称类型
    all_types = set(names1.keys()) | set(names2.keys())
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"📄 文件1: {file1}\n")
            f.write(f"📄 文件2: {file2}\n")
            f.write(f"🔍 比较的名称类型: {', '.join(sorted(all_types))}\n\n")
            
            for name_type in sorted(all_types):
                f.write(f"{'='*50}\n")
                f.write(f"🏷️  {name_type} 比较结果\n")
                f.write(f"{'='*50}\n")
                
                set1 = names1.get(name_type, set())
                set2 = names2.get(name_type, set())
                
                added = set2 - set1
                removed = set1 - set2
                common = set1 & set2
                
                f.write(f"📊 统计:\n")
                f.write(f"   文件1中的{name_type}: {len(set1)}\n")
                f.write(f"   文件2中的{name_type}: {len(set2)}\n")
                f.write(f"   新增{name_type}: {len(added)}\n")
                f.write(f"   删除{name_type}: {len(removed)}\n")
                f.write(f"   共同{name_type}: {len(common)}\n\n")
                
                if added:
                    f.write(f"➕ 新增{name_type}（在文件2中存在，文件1中不存在）:\n")
                    for name in sorted(added):
                        f.write(f"  + {name}\n")
                    f.write("\n")
                
                if removed:
                    f.write(f"➖ 删除{name_type}（在文件1中存在，文件2中不存在）:\n")
                    for name in sorted(removed):
                        f.write(f"  - {name}\n")
                    f.write("\n")
                
                if common:
                    f.write(f"🔄 共同{name_type}（两个文件中都存在）:\n")
                    for name in sorted(common):
                        f.write(f"    {name}\n")
                    f.write("\n")
                
                f.write("\n")
        
        print(f"比较结果已写入文件: {output_file}")
        
        # 输出简要统计到控制台
        print(f"\n📊 简要统计:")
        for name_type in sorted(all_types):
            set1 = names1.get(name_type, set())
            set2 = names2.get(name_type, set())
            added = len(set2 - set1)
            removed = len(set1 - set2)
            common = len(set1 & set2)
            print(f"  {name_type}: 新增{added}, 删除{removed}, 共同{common}")
            
    except Exception as e:
        print(f"无法写入输出文件 {output_file}: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="比较两个文本文件中的名称差异，支持多种类型（属性名、方法名、代理名等），并将结果写入输出文件。",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
支持的名称类型:
  属性名, 方法名, 代理名, 枚举名, 常量名, Block名, 文件名

使用示例:
  python zzzzz.py file1.txt file2.txt output.txt
  python zzzzz.py file1.txt file2.txt output.txt --types 属性名 方法名
  python zzzzz.py file1.txt file2.txt output.txt --types 属性名 --verbose
        """
    )
    
    parser.add_argument("file1", help="第一个文本文件的路径")
    parser.add_argument("file2", help="第二个文本文件的路径")
    parser.add_argument("output_file", help="输出结果的文件路径")
    parser.add_argument(
        "--types", 
        nargs="+", 
        help="指定要比较的名称类型，可选: 属性名 方法名 代理名 枚举名 常量名 Block名 文件名",
        default=None
    )
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="显示详细的处理信息"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        print(f"正在比较文件: {args.file1} 和 {args.file2}")
        if args.types:
            print(f"指定的名称类型: {', '.join(args.types)}")
        else:
            print("将提取所有支持的名称类型")
    
    compare_names(args.file1, args.file2, args.output_file, args.types)

if __name__ == "__main__":
    main()
