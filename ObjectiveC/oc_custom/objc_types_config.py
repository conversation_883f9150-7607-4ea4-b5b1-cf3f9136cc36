#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Objective-C 系统框架类型配置文件
基于iOS/macOS系统框架的常用类型
"""

# Foundation框架类型
FOUNDATION_TYPES = [
    # 基础类型
    'NSObject', 'NSProxy',
    
    # 字符串和数据
    'NSString', 'NSMutableString', 'NSAttributedString', 'NSMutableAttributedString',
    'NSData', 'NSMutableData', 'NSPurgeableData',
    
    # 集合类型
    'NSArray', 'NSMutableArray', 'NSPointerArray',
    'NSDictionary', 'NSMutableDictionary', 'NSMapTable',
    'NSSet', 'NSMutableSet', 'NSCountedSet', 'NSOrderedSet', 'NSMutableOrderedSet',
    'NSHashTable', 'NSPointerFunctions',
    
    # 数值类型
    'NSNumber', 'NSDecimalNumber', 'NSNumberFormatter',
    'NSValue', 'NSNull',
    
    # 日期和时间
    'NSDate', 'NSDateComponents', 'NSDateFormatter', 'NSDateInterval',
    'NSCalendar', 'NSTimeZone', 'NSLocale',
    'NSTimer', 'NSDateComponentsFormatter',
    
    # URL和网络
    'NSURL', 'NSURLComponents', 'NSURLQueryItem', 'NSURLRequest', 'NSMutableURLRequest',
    'NSURLResponse', 'NSHTTPURLResponse', 'NSURLSession', 'NSURLSessionTask',
    'NSURLSessionDataTask', 'NSURLSessionUploadTask', 'NSURLSessionDownloadTask',
    'NSURLConnection', 'NSURLCache', 'NSHTTPCookieStorage', 'NSHTTPCookie',
    
    # 文件系统
    'NSFileManager', 'NSFileHandle', 'NSFileWrapper', 'NSDirectoryEnumerator',
    'NSBundle', 'NSProcessInfo', 'NSPipe', 'NSTask', 'NSFileCoordinator',
    
    # 错误处理
    'NSError', 'NSException', 'NSAssertionHandler',
    
    # 线程和并发
    'NSThread', 'NSRunLoop', 'NSOperation', 'NSOperationQueue', 'NSBlockOperation',
    'NSInvocationOperation', 'NSCondition', 'NSConditionLock', 'NSLock', 'NSRecursiveLock',
    'NSDistributedLock', 'NSSemaphore',
    
    # 通知和观察
    'NSNotification', 'NSNotificationCenter', 'NSDistributedNotificationCenter',
    'NSNotificationQueue', 'NSKeyValueObservation',
    
    # 序列化
    'NSCoder', 'NSKeyedArchiver', 'NSKeyedUnarchiver', 'NSArchiver', 'NSUnarchiver',
    'NSPropertyListSerialization', 'NSJSONSerialization',
    
    # 正则表达式
    'NSRegularExpression', 'NSTextCheckingResult', 'NSDataDetector',
    
    # 其他工具类
    'NSScanner', 'NSFormatter', 'NSByteCountFormatter', 'NSLengthFormatter',
    'NSMassFormatter', 'NSEnergyFormatter', 'NSPersonNameComponents',
    'NSPersonNameComponentsFormatter', 'NSMeasurement', 'NSUnit', 'NSDimension',
    'NSUnitConverter', 'NSProgress', 'NSUserDefaults', 'NSUserActivity',
    'NSExtensionContext', 'NSExtensionItem', 'NSItemProvider',
    
    # 流和管道
    'NSStream', 'NSInputStream', 'NSOutputStream', 'NSHost', 'NSNetService',
    'NSNetServiceBrowser', 'NSPort', 'NSMachPort', 'NSMessagePort', 'NSSocketPort',
    
    # 表达式和谓词
    'NSExpression', 'NSPredicate', 'NSComparisonPredicate', 'NSCompoundPredicate',
    
    # 索引和排序
    'NSIndexPath', 'NSIndexSet', 'NSMutableIndexSet', 'NSSortDescriptor',
    
    # UUID和标识符
    'NSUUID', 'NSUserNotification', 'NSUserNotificationCenter'
]

# UIKit框架类型
UIKIT_TYPES = [
    # 视图基础
    'UIView', 'UIWindow', 'UIScreen', 'UIViewController', 'UINavigationController',
    'UITabBarController', 'UISplitViewController', 'UIPageViewController',
    'UIPopoverController', 'UIPopoverPresentationController', 'UIPresentationController',
    
    # 控件
    'UILabel', 'UIButton', 'UIImageView', 'UITextField', 'UITextView', 'UISearchBar',
    'UISlider', 'UISwitch', 'UIStepper', 'UISegmentedControl', 'UIProgressView',
    'UIActivityIndicatorView', 'UIPickerView', 'UIDatePicker', 'UIPageControl',
    'UIRefreshControl', 'UIStackView',
    
    # 滚动视图
    'UIScrollView', 'UITableView', 'UICollectionView', 'UIWebView', 'WKWebView',
    
    # 表格视图
    'UITableViewCell', 'UITableViewHeaderFooterView', 'UITableViewController',
    
    # 集合视图
    'UICollectionViewCell', 'UICollectionReusableView', 'UICollectionViewController',
    'UICollectionViewLayout', 'UICollectionViewFlowLayout',
    'UICollectionViewLayoutAttributes', 'UICollectionViewTransitionLayout',
    
    # 导航
    'UINavigationBar', 'UINavigationItem', 'UIBarButtonItem', 'UIToolbar',
    'UITabBar', 'UITabBarItem', 'UISearchController', 'UISearchDisplayController',
    
    # 弹窗和提示
    'UIAlertController', 'UIAlertView', 'UIActionSheet', 'UIAlertAction',
    'UIPopoverController', 'UIMenuController', 'UIMenuItem',
    
    # 图像和颜色
    'UIImage', 'UIImageAsset', 'UIColor', 'UIFont', 'UIFontDescriptor',
    'UIBezierPath', 'UIGraphicsRenderer', 'UIGraphicsImageRenderer',
    
    # 手势识别
    'UIGestureRecognizer', 'UITapGestureRecognizer', 'UIPinchGestureRecognizer',
    'UIRotationGestureRecognizer', 'UISwipeGestureRecognizer', 'UIPanGestureRecognizer',
    'UILongPressGestureRecognizer', 'UIScreenEdgePanGestureRecognizer',
    
    # 动画
    'UIViewPropertyAnimator', 'UISpringTimingParameters',
    'UICubicTimingParameters', 'UIViewAnimatingState',
    
    # 应用程序
    'UIApplication', 'UIApplicationDelegate', 'UIScene', 'UISceneDelegate',
    'UIWindowScene', 'UISceneSession', 'UISceneConfiguration',
    
    # 状态栏和导航栏
    'UIStatusBarManager', 'UINavigationBarAppearance', 'UIBarAppearance',
    'UITabBarAppearance',
    
    # 输入和键盘
    'UITextInput', 'UITextInputTraits', 'UITextPosition', 'UITextRange',
    'UITextSelectionRect', 'UIMenuController', 'UITextChecker',
    
    # 拖拽
    'UIDragItem', 'UIDragPreview', 'UIDragSession', 'UIDropProposal',
    'UIDragInteraction', 'UIDropInteraction',
    
    # 其他UI组件
    'UIVisualEffectView', 'UIBlurEffect', 'UIVibrancyEffect',
    'UIMotionEffect', 'UIInterpolatingMotionEffect', 'UIMotionEffectGroup',
    'UIFeedbackGenerator', 'UIImpactFeedbackGenerator', 'UISelectionFeedbackGenerator',
    'UINotificationFeedbackGenerator'
]

# Core Graphics类型
CORE_GRAPHICS_TYPES = [
    'CGContext', 'CGPath', 'CGMutablePath', 'CGImage', 'CGImageSource',
    'CGImageDestination', 'CGColorSpace', 'CGColor', 'CGGradient',
    'CGShading', 'CGPattern', 'CGFont', 'CGDataProvider', 'CGDataConsumer',
    'CGPDFDocument', 'CGPDFPage', 'CGLayer', 'CGFunction'
]

# Core Animation类型
CORE_ANIMATION_TYPES = [
    'CALayer', 'CAScrollLayer', 'CATextLayer', 'CAShapeLayer', 'CAGradientLayer',
    'CAReplicatorLayer', 'CATransformLayer', 'CAEmitterLayer', 'CAEmitterCell',
    'CAAnimation', 'CABasicAnimation', 'CAKeyframeAnimation', 'CAAnimationGroup',
    'CATransition', 'CASpringAnimation', 'CADisplayLink', 'CAMediaTiming',
    'CAMediaTimingFunction', 'CAValueFunction'
]

# Core Data类型
CORE_DATA_TYPES = [
    'NSManagedObject', 'NSManagedObjectContext', 'NSManagedObjectModel',
    'NSPersistentStore', 'NSPersistentStoreCoordinator', 'NSPersistentContainer',
    'NSEntityDescription', 'NSAttributeDescription', 'NSRelationshipDescription',
    'NSFetchRequest', 'NSFetchedResultsController', 'NSMigrationManager', 'NSMappingModel'
]

# AVFoundation类型
AVFOUNDATION_TYPES = [
    'AVPlayer', 'AVPlayerItem', 'AVPlayerLayer', 'AVPlayerViewController',
    'AVAudioPlayer', 'AVAudioRecorder', 'AVAudioSession', 'AVAudioEngine',
    'AVCaptureSession', 'AVCaptureDevice', 'AVCaptureInput', 'AVCaptureOutput',
    'AVCaptureVideoPreviewLayer', 'AVAsset', 'AVURLAsset', 'AVComposition',
    'AVVideoComposition', 'AVAudioMix'
]

# MapKit类型
MAPKIT_TYPES = [
    'MKMapView', 'MKAnnotation', 'MKAnnotationView', 'MKPinAnnotationView',
    'MKUserLocation', 'MKPlacemark', 'MKMapItem', 'MKDirections',
    'MKRoute', 'MKPolyline', 'MKPolygon', 'MKCircle', 'MKOverlay',
    'MKOverlayRenderer', 'MKTileOverlay', 'MKLocalSearch'
]

# 其他常用框架类型
OTHER_FRAMEWORK_TYPES = [
    # GameKit
    'GKLocalPlayer', 'GKAchievement', 'GKLeaderboard', 'GKScore',
    
    # StoreKit
    'SKProduct', 'SKPayment', 'SKPaymentTransaction', 'SKPaymentQueue',
    'SKProductsRequest', 'SKStoreProductViewController',
    
    # Social
    'SLComposeViewController', 'SLRequest',
    
    # MessageUI
    'MFMailComposeViewController', 'MFMessageComposeViewController',
    
    # EventKit
    'EKEventStore', 'EKEvent', 'EKCalendar', 'EKReminder',
    
    # AddressBook
    'ABAddressBook', 'ABPerson', 'ABRecord',
    
    # Contacts
    'CNContact', 'CNContactStore', 'CNContactFormatter',
    
    # Photos
    'PHAsset', 'PHAssetCollection', 'PHPhotoLibrary', 'PHImageManager',
    
    # HealthKit
    'HKHealthStore', 'HKSample', 'HKQuantitySample', 'HKWorkout',
    
    # CloudKit
    'CKContainer', 'CKDatabase', 'CKRecord', 'CKRecordZone',
    
    # UserNotifications
    'UNUserNotificationCenter', 'UNNotificationRequest', 'UNNotificationContent'
]

def get_all_pointer_types():
    """获取所有指针类型"""
    return (FOUNDATION_TYPES + UIKIT_TYPES + CORE_GRAPHICS_TYPES + 
            CORE_ANIMATION_TYPES + CORE_DATA_TYPES + AVFOUNDATION_TYPES + 
            MAPKIT_TYPES + OTHER_FRAMEWORK_TYPES)

def get_foundation_types():
    """获取Foundation框架类型"""
    return FOUNDATION_TYPES

def get_uikit_types():
    """获取UIKit框架类型"""
    return UIKIT_TYPES

def get_types_by_framework(framework_name):
    """根据框架名获取类型"""
    framework_map = {
        'Foundation': FOUNDATION_TYPES,
        'UIKit': UIKIT_TYPES,
        'CoreGraphics': CORE_GRAPHICS_TYPES,
        'CoreAnimation': CORE_ANIMATION_TYPES,
        'CoreData': CORE_DATA_TYPES,
        'AVFoundation': AVFOUNDATION_TYPES,
        'MapKit': MAPKIT_TYPES,
        'Other': OTHER_FRAMEWORK_TYPES
    }
    return framework_map.get(framework_name, [])

if __name__ == "__main__":
    # 测试代码
    all_types = get_all_pointer_types()
    print(f"总共有 {len(all_types)} 个类型")
    print(f"Foundation: {len(FOUNDATION_TYPES)} 个")
    print(f"UIKit: {len(UIKIT_TYPES)} 个")
    print(f"其他框架: {len(OTHER_FRAMEWORK_TYPES)} 个")
