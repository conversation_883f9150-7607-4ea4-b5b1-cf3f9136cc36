#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的 custom_core.py 功能
验证常量、宏定义、局部变量提取的优化效果
"""

import sys
import os

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from ObjectiveC.oc_custom import custom_core
except ImportError:
    # 如果无法导入，尝试直接导入
    import custom_core

def test_pointer_types():
    """测试统一的 pointer_types 获取功能"""
    print("=" * 60)
    print("测试 pointer_types 获取功能")
    print("=" * 60)
    
    pointer_types = custom_core.get_pointer_types()
    print(f"获取到 {len(pointer_types)} 个指针类型")
    print("前10个类型（按长度降序）:")
    for i, ptype in enumerate(pointer_types[:10]):
        print(f"  {i+1:2d}. {ptype} (长度: {len(ptype)})")
    print()

def test_constants_extraction():
    """测试常量提取功能"""
    print("=" * 60)
    print("测试常量提取功能")
    print("=" * 60)
    
    # 测试代码示例
    test_content = '''
    // 静态常量
    static NSString *const kTestConstant = @"test";
    static BOOL _sendLog = YES;
    static NSInteger maxCount = 100;
    
    // extern 常量
    extern NSString *const kExternalConstant;
    extern BOOL isDebugMode;
    
    // 宏定义
    #define MAX_SIZE 1024
    #define DEBUG_LOG(fmt, ...) NSLog(fmt, ##__VA_ARGS__)
    #define IS_IPHONE (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone)
    
    // 复杂宏定义
    #define COMPLEX_MACRO(x, y) \\
        do { \\
            NSLog(@"x: %@, y: %@", x, y); \\
        } while(0)
    '''
    
    constants = custom_core.process_constants_content(test_content)
    print(f"提取到 {len(constants)} 个常量:")
    for i, constant in enumerate(constants):
        print(f"  {i+1:2d}. {constant}")
    print()

def test_local_variables_extraction():
    """测试局部变量提取功能"""
    print("=" * 60)
    print("测试局部变量提取功能")
    print("=" * 60)
    
    # 测试代码示例
    test_content = '''
    - (void)testMethod {
        NSString *name = @"test";
        NSArray<NSString *> *items = @[];
        BOOL isValid = YES;
        NSInteger count = 10;
        
        for (NSInteger i = 0; i < count; i++) {
            NSLog(@"index: %ld", i);
        }
        
        for (NSString *item in items) {
            NSLog(@"item: %@", item);
        }
    }
    '''
    
    local_vars = custom_core.process_local_variables_content(test_content)
    print(f"提取到 {len(local_vars)} 个局部变量:")
    for i, var in enumerate(local_vars):
        print(f"  {i+1:2d}. {var}")
    print()

def test_unified_extraction():
    """测试统一变量提取功能"""
    print("=" * 60)
    print("测试统一变量提取功能")
    print("=" * 60)

    # 测试代码示例
    test_content = '''
    static NSString *staticVar = @"static";
    extern NSArray *externVar;

    - (void)method {
        NSString *localVar = @"local";
        BOOL flag = YES;
        for (NSInteger i = 0; i < 10; i++) {
            NSLog(@"index: %ld", i);
        }
        for (NSString *item in items) {
            NSLog(@"item: %@", item);
        }
    }
    '''

    # 测试不同类型的提取
    static_vars = custom_core.extract_variables_unified(test_content, ['static'])
    extern_vars = custom_core.extract_variables_unified(test_content, ['const'])
    local_vars = custom_core.extract_variables_unified(test_content, ['local'])

    print(f"Static 变量: {static_vars}")
    print(f"Extern 变量: {extern_vars}")
    print(f"Local 变量: {local_vars}")
    print()

def test_process_local_vs_unified():
    """测试 process_local_variables_content 与 extract_variables_unified 的一致性"""
    print("=" * 60)
    print("测试 process_local_variables_content 与 extract_variables_unified 的一致性")
    print("=" * 60)

    # 测试代码示例
    test_content = '''
    - (void)complexMethod {
        NSString *name = @"test";
        NSArray<NSString *> *items = @[];
        BOOL isValid = YES;
        NSInteger count = 10;
        CGPoint point = CGPointMake(0, 0);

        for (NSInteger i = 0; i < count; i++) {
            NSLog(@"index: %ld", i);
        }

        for (NSString *item in items) {
            NSLog(@"item: %@", item);
        }

        CustomClass *custom = [[CustomClass alloc] init];
    }
    '''

    # 使用两种方法提取局部变量
    local_vars_process = custom_core.process_local_variables_content(test_content)
    local_vars_unified = custom_core.extract_variables_unified(test_content, ['local'])

    print(f"process_local_variables_content 结果: {local_vars_process}")
    print(f"extract_variables_unified 结果: {local_vars_unified}")
    print(f"结果是否一致: {local_vars_process == local_vars_unified}")
    print()

def debug_pattern_matching():
    """调试模式匹配"""
    print("=" * 60)
    print("调试模式匹配")
    print("=" * 60)

    # 测试具体的声明
    test_cases = [
        "ATTrackingManagerAuthorizationStatus currentStatus = [ATTrackingManager trackingAuthorizationStatus];",
        "NSFileCoordinator *zb_coordinator = [[NSFileCoordinator alloc] initWithFilePresenter:nil];",
        "NSURL *zb_directoryURL = zb_url.URLByDeletingLastPathComponent;",
        "NSFileHandle *zb_fileHandle = [NSFileHandle fileHandleForWritingToURL:zb_url error:&youStood];",
        "^(ATTrackingManagerAuthorizationStatus status) {",
        "byAccessor:^(NSURL * _Nonnull zb_newURL) {"
    ]

    import re

    for test_case in test_cases:
        print(f"测试: {test_case}")

        # 测试各种模式
        patterns = [
            (r'([A-Z]\w{10,})\s+(\w+)\s*=', "长类型名模式"),
            (r'([A-Z]\w+)\s*\*\s*(\w+)\s*=', "指针类型模式"),
            (r'\^[^{]*\(\s*[A-Z]\w+\s+(\w+)\s*\)', "block参数模式"),
            (r'byAccessor:\s*\^\s*\(\s*\w+\s*\*\s*\w+\s+(\w+)\s*\)', "byAccessor模式"),
            (r'NSFileCoordinator\s*\*\s*(\w+)\s*=', "NSFileCoordinator模式"),
            (r'NSURL\s*\*\s*(\w+)\s*=', "NSURL模式"),
            (r'NSFileHandle\s*\*\s*(\w+)\s*=', "NSFileHandle模式")
        ]

        matched = False
        for pattern, name in patterns:
            matches = re.findall(pattern, test_case)
            if matches:
                print(f"  ✅ {name}: {matches}")
                matched = True

        if not matched:
            print(f"  ❌ 没有模式匹配")
        print()

def test_problematic_cases():
    """测试用户报告的问题案例"""
    print("=" * 60)
    print("测试用户报告的问题案例")
    print("=" * 60)

    # 问题案例1：ATTrackingManagerAuthorizationStatus 被误识别
    test_content1 = '''
    + (void)__waitingForUserOperationAuthorization:(void (^)(void))complate currentAttempt:(NSInteger)currentAttempt {
        NSInteger retryCount = 10;

        if (@available(iOS 14, *)) {
            ATTrackingManagerAuthorizationStatus currentStatus = [ATTrackingManager trackingAuthorizationStatus];
        }
    }

    + (void)__performATTRequest:(void (^)(void))completion {
        if (@available(iOS 14, *)) {
            [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
                ATTrackingManagerAuthorizationStatus currentStatus = [ATTrackingManager trackingAuthorizationStatus];
            }];
        }
    }
    '''

    # 问题案例2：zb_directoryURL 和 zb_fileHandle 没有被提取
    test_content2 = '''
    - (BOOL)skipSink:(NSData *)zb_data moire:(NSURL *)zb_url {
        __block BOOL lossHourly = NO;
        NSFileCoordinator *zb_coordinator = [[NSFileCoordinator alloc] initWithFilePresenter:nil];
        NSError *youStood = nil;
        [zb_coordinator coordinateWritingItemAtURL:zb_url options:0 error:&youStood byAccessor:^(NSURL * _Nonnull zb_newURL) {

            NSError *youStood = nil;

            if (![[NSFileManager defaultManager] fileExistsAtPath:zb_url.path]) {

                NSURL *zb_directoryURL = zb_url.URLByDeletingLastPathComponent;
                if (![[NSFileManager defaultManager] fileExistsAtPath:zb_directoryURL.path]) {
                    [[NSFileManager defaultManager] createDirectoryAtURL:zb_directoryURL withIntermediateDirectories:YES attributes:nil error:&youStood];
                }

                [[NSFileManager defaultManager] createFileAtPath:zb_url.path contents:nil attributes:nil];
            }

            NSFileHandle *zb_fileHandle = [NSFileHandle fileHandleForWritingToURL:zb_url error:&youStood];
            [zb_fileHandle seekToEndOfFile];
            [zb_fileHandle writeData:zb_data];
            if (imperialInfinitySmileDidWorkoutsOff) {
                [zb_fileHandle synchronizeFile];
            }
            [zb_fileHandle closeFile];

            if (youStood) {

            }else {
                lossHourly = YES;
            }

        }];

        if (youStood) {

        }

        return lossHourly;
    }
    '''

    print("问题案例1 - ATTrackingManagerAuthorizationStatus:")
    vars1 = custom_core.process_local_variables_content(test_content1)
    print(f"提取结果: {vars1}")
    if 'ATTrackingManagerAuthorizationStatus' in vars1:
        print("❌ 错误：类型名被误识别为变量名")
    else:
        print("✅ 正确：没有误识别类型名")

    # 分析缺失的变量：
    # currentAttempt - 方法参数，不在方法体内，不应该被提取
    # currentStatus - ATTrackingManagerAuthorizationStatus currentStatus = [ATTrackingManager trackingAuthorizationStatus];
    # retryCount - NSInteger retryCount = 10;
    # status - block 参数：^(ATTrackingManagerAuthorizationStatus status)
    expected_vars1 = ['currentStatus', 'retryCount', 'status']
    missing_vars1 = [v for v in expected_vars1 if v not in vars1]
    if missing_vars1:
        print(f"❌ 缺失变量: {missing_vars1}")
        print("分析缺失原因:")
        if 'currentStatus' in missing_vars1:
            print("  - currentStatus: 长类型名声明模式未匹配")
        if 'status' in missing_vars1:
            print("  - status: block 参数模式未匹配")
    else:
        print("✅ 所有预期变量都被正确提取")
    print()

    print("问题案例2 - zb_directoryURL 和 zb_fileHandle:")
    vars2 = custom_core.process_local_variables_content(test_content2)
    print(f"提取结果: {vars2}")

    expected_vars2 = ['lossHourly', 'youStood', 'zb_coordinator', 'zb_directoryURL', 'zb_fileHandle', 'zb_newURL']
    missing_vars2 = [v for v in expected_vars2 if v not in vars2]
    if missing_vars2:
        print(f"❌ 缺失变量: {missing_vars2}")
    else:
        print("✅ 所有预期变量都被正确提取")
    print()

def main():
    """主测试函数"""
    print("开始测试重构后的 custom_core.py 功能")
    print()

    try:
        test_pointer_types()
        test_constants_extraction()
        test_local_variables_extraction()
        test_unified_extraction()
        test_process_local_vs_unified()
        debug_pattern_matching()
        test_problematic_cases()

        print("=" * 60)
        print("所有测试完成！")
        print("=" * 60)

    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
