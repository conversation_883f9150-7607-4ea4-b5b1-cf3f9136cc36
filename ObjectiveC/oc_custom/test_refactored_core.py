#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的 custom_core.py 功能
验证常量、宏定义、局部变量提取的优化效果
"""

import sys
import os

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

try:
    from ObjectiveC.oc_custom import custom_core
except ImportError:
    # 如果无法导入，尝试直接导入
    import custom_core

def test_pointer_types():
    """测试统一的 pointer_types 获取功能"""
    print("=" * 60)
    print("测试 pointer_types 获取功能")
    print("=" * 60)
    
    pointer_types = custom_core.get_pointer_types()
    print(f"获取到 {len(pointer_types)} 个指针类型")
    print("前10个类型（按长度降序）:")
    for i, ptype in enumerate(pointer_types[:10]):
        print(f"  {i+1:2d}. {ptype} (长度: {len(ptype)})")
    print()

def test_constants_extraction():
    """测试常量提取功能"""
    print("=" * 60)
    print("测试常量提取功能")
    print("=" * 60)
    
    # 测试代码示例
    test_content = '''
    // 静态常量
    static NSString *const kTestConstant = @"test";
    static BOOL _sendLog = YES;
    static NSInteger maxCount = 100;
    
    // extern 常量
    extern NSString *const kExternalConstant;
    extern BOOL isDebugMode;
    
    // 宏定义
    #define MAX_SIZE 1024
    #define DEBUG_LOG(fmt, ...) NSLog(fmt, ##__VA_ARGS__)
    #define IS_IPHONE (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone)
    
    // 复杂宏定义
    #define COMPLEX_MACRO(x, y) \\
        do { \\
            NSLog(@"x: %@, y: %@", x, y); \\
        } while(0)
    '''
    
    constants = custom_core.process_constants_content(test_content)
    print(f"提取到 {len(constants)} 个常量:")
    for i, constant in enumerate(constants):
        print(f"  {i+1:2d}. {constant}")
    print()

def test_local_variables_extraction():
    """测试局部变量提取功能"""
    print("=" * 60)
    print("测试局部变量提取功能")
    print("=" * 60)
    
    # 测试代码示例
    test_content = '''
    - (void)testMethod {
        NSString *name = @"test";
        NSArray<NSString *> *items = @[];
        BOOL isValid = YES;
        NSInteger count = 10;
        
        for (NSInteger i = 0; i < count; i++) {
            NSLog(@"index: %ld", i);
        }
        
        for (NSString *item in items) {
            NSLog(@"item: %@", item);
        }
    }
    '''
    
    local_vars = custom_core.process_local_variables_content(test_content)
    print(f"提取到 {len(local_vars)} 个局部变量:")
    for i, var in enumerate(local_vars):
        print(f"  {i+1:2d}. {var}")
    print()

def test_unified_extraction():
    """测试统一变量提取功能"""
    print("=" * 60)
    print("测试统一变量提取功能")
    print("=" * 60)
    
    # 测试代码示例
    test_content = '''
    static NSString *staticVar = @"static";
    extern NSArray *externVar;
    
    - (void)method {
        NSString *localVar = @"local";
        BOOL flag = YES;
    }
    '''
    
    # 测试不同类型的提取
    static_vars = custom_core.extract_variables_unified(test_content, ['static'])
    extern_vars = custom_core.extract_variables_unified(test_content, ['const'])
    local_vars = custom_core.extract_variables_unified(test_content, ['local'])
    
    print(f"Static 变量: {static_vars}")
    print(f"Extern 变量: {extern_vars}")
    print(f"Local 变量: {local_vars}")
    print()

def main():
    """主测试函数"""
    print("开始测试重构后的 custom_core.py 功能")
    print()
    
    try:
        test_pointer_types()
        test_constants_extraction()
        test_local_variables_extraction()
        test_unified_extraction()
        
        print("=" * 60)
        print("所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
